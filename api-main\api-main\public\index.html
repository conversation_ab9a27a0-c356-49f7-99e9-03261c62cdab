<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>网易云音乐 API Reborn</title>
  <style>
    html, body {
      height: 100vh;
      width: 100vw;
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #e0eafc 0%, #cfdef3 100%);
      font-family: 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif;
      color: #333;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    .container {
      background: rgba(255,255,255,0.95);
      padding: 2.5em 2em 2em 2em;
      border-radius: 18px;
      box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.18);
      max-width: 520px;
      width: 95vw;
      margin-top: 2em;
      margin-bottom: 2em;
      position: relative;
      overflow: hidden;
      animation: fadeIn 1.2s cubic-bezier(.39,.575,.565,1) both;
    }

    @keyframes fadeIn {
      0% { opacity: 0; transform: translateY(30px); }
      100% { opacity: 1; transform: translateY(0); }
    }

    h1 {
      font-size: 2.3em;
      margin-bottom: 0.5em;
      color: #2d8cf0;
      letter-spacing: 1px;
      font-weight: 700;
      text-shadow: 0 2px 8px #e0eafc;
    }

    .version {
      font-size: 1.1em;
      color: #888;
      margin-left: 0.5em;
    }

    p {
      font-size: 1.1em;
      margin-bottom: 1.2em;
      color: #555;
      line-height: 1.7;
    }

    .current-url {
      font-size: 0.98em;
      color: #aaa;
      margin-bottom: 1.2em;
      word-break: break-all;
    }

    a {
      color: #2d8cf0;
      text-decoration: none;
      transition: color 0.2s;
      font-weight: 500;
    }
    a:hover {
      color: #42b983;
      text-decoration: underline;
    }

    h2 {
      font-size: 1.3em;
      margin: 1.5em 0 0.7em 0;
      color: #2d8cf0;
      font-weight: 600;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;
      text-align: left;
      width: 100%;
    }

    li {
      margin: 0.6em 0;
      line-height: 1.7;
      display: flex;
      align-items: center;
      background: #f4f8fb;
      border-radius: 6px;
      padding: 0.5em 1em;
      transition: box-shadow 0.2s;
      box-shadow: 0 1px 2px rgba(45,140,240,0.04);
    }
    li:hover {
      box-shadow: 0 4px 12px rgba(45,140,240,0.10);
      background: #eaf4fb;
    }

    .number {
      margin-right: 12px;
      color: #2d8cf0;
      font-weight: bold;
      font-size: 1.1em;
      min-width: 2em;
      display: inline-block;
      text-align: right;
    }

    .footer {
      margin-top: 2em;
      color: #aaa;
      font-size: 0.98em;
      text-align: center;
      letter-spacing: 0.5px;
    }
  </style>
</head>

<body>
  <div class="container">
    <h1>网易云音乐 API <span id="api-version" class="version"></span></h1>
    <p>🎵 这是一个持续维护的第三方网易云音乐 API 服务，兼容原版接口，支持多平台部署。</p>
    <p>当你看到这个页面时，服务已成功运行！</p>
    <p class="current-url">当前访问：<span id="current-url"></span></p>
    <a href="/docs" target="_blank">📚 查看接口文档</a>
    <h2>常用示例</h2>
    <ul id="example-list">
      <li><a href="./search?keywords=这么可爱真是抱歉">搜索歌曲</a></li>
      <li><a href="./song/url/unblock?id=1969519579">歌曲解灰</a></li>
      <li><a href="./comment/music?id=1969519579&limit=1">获取歌曲评论</a></li>
      <li><a href="./dj/program?rid=336355127">获取电台节目</a></li>
      <li><a href="./qrlogin.html">二维码登录演示</a></li>
      <li><a href="./audio_match_demo/index.html">听歌识曲 Demo</a></li>
      <li><a href="./cloud.html">云盘上传 Demo</a></li>
      <li><a href="./eapi_decrypt.html">EAPI 参数/返回值解析</a></li>
      <li><a href="./api.html">API 调试界面</a></li>
      <li><a href="./playlist_import.html">歌单导入工具</a></li>
    </ul>
    <div class="footer">
      <span>© 2025 网易云音乐 API Reborn &nbsp;|&nbsp; <a href="https://github.com/NeteaseCloudMusicApiReborn/api" target="_blank">GitHub</a></span>
    </div>
  </div>

  <script src="https://fastly.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const currentUrlElement = document.getElementById('current-url');
      currentUrlElement.textContent = window.location.href;

      const exampleList = document.getElementById('example-list');
      const items = exampleList.getElementsByTagName('li');

      for (let i = 0; i < items.length; i++) {
        const number = document.createElement('span');
        number.className = 'number';
        number.textContent = (i + 1) + '. ';
        items[i].prepend(number);
      }
      
      // 获取API版本号
      axios({
        url: '/inner/version',
        method: 'post',
        data: {},
      }).then((res) => {
        const body = res.data;
        const version = body.data.version;
        const apiVersionElement = document.getElementById('api-version');
        apiVersionElement.textContent = ` v${version}`;
      });
    });
  </script>
</body>

</html>