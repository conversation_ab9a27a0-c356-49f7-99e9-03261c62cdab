{"name": "hydrogenmusic", "private": true, "version": "0.5.0", "main": "background.js", "author": "<PERSON><PERSON><PERSON><PERSON>", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "nodemon --exec electron . --watch ./ --ext .html", "dist": "vite build && electron-builder -p always"}, "dependencies": {"axios": "^1.2.0", "dayjs": "^1.11.6", "electron-store": "^8.1.0", "electron-updater": "^5.3.0", "electron-win-state": "^1.1.22", "fs-extra": "^11.1.0", "howler": "^2.2.3", "js-cookie": "^3.0.1", "js-md5": "^0.7.3", "music-metadata": "^7.13.0", "nanoid": "^3.3.4", "NeteaseCloudMusicApi": "^4.8.10", "normalize.css": "^8.0.1", "pinia": "^2.0.23", "pinia-plugin-persistedstate": "^3.0.1", "plyr": "^3.7.3", "qrcode": "^1.5.1", "vue": "^3.2.41", "vue-router": "^4.1.6", "vue-slider-component": "^4.1.0-beta.6", "vue-virtual-scroller": "^2.0.0-beta.8"}, "devDependencies": {"@vitejs/plugin-vue": "^3.2.0", "electron": "^21.2.0", "electron-builder": "^23.6.0", "nodemon": "^2.0.20", "sass": "^1.56.1", "vite": "^3.2.0"}, "build": {"productName": "Hydrogen Music", "appId": "114514", "asar": true, "directories": {"output": "release/${version}"}, "files": [], "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}, "mac": {"category": ""}, "win": {"icon": "./src/assets/icon/icon.ico", "target": ["nsis", "portable", "zip"], "verifyUpdateCodeSignature": false}, "linux": {}, "publish": [{"provider": "github", "owner": "<PERSON><PERSON><PERSON><PERSON>", "repo": "Hydrogen-Music", "releaseType": "draft"}]}}