<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="KEYWords" contect="网易云音乐,网易云音乐 api,网易云音乐 nodejs,网易云音乐 node.js">
  <meta name="description" content="网易云音乐 NodeJS API Reborn">
  <title>网易云音乐 NodeJS API Reborn</title>
  <link rel="icon" href="favicon.ico">
  <meta name="description" content="Description">
  <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta name="referrer" content="never">
  <link rel="stylesheet" href="//unpkg.com/docsify/lib/themes/vue.css">
  <script async src="//pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
  <script>
    (adsbygoogle = window.adsbygoogle || []).push({
      google_ad_client: "ca-pub-5159844745975514",
      enable_page_level_ads: true
    });
  </script>
</head>
<body>
  <div id="app"></div>
</body>
<script>
  window.$docsify = {
    name: '网易云音乐 API Reborn',
    repo: 'https://github.com/NeteaseCloudMusicApiReborn/api',
    coverpage: true,
    homepage: 'home.md',
  }
</script>
<script src="https://unpkg.com/docsify@4.11.3/lib/docsify.min.js"></script>
<script>
  if (typeof navigator.serviceWorker !== 'undefined') {
    navigator.serviceWorker.register('sw.js')
  }
</script>
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=UA-139996012-1"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'UA-139996012-1');
</script>
</html>
