<br />
<p align="center">
  <a href="https://github.com/Kaidesuyo/Hydrogen-Music" target="blank">
    <img src="images/icon.png" alt="Logo" width="156" height="156">
  </a>
  <h2 align="center" style="font-weight: 600">Hydrogen Music</h2>

  <p align="center">
    ⚠️请注意：本项目只是为了尽快应付毕业设计的产物，完全没有根据软件工程规范编写，仅供参考！
    <br />
    <a href="#%EF%B8%8F-安装" target="blank"><strong>📦️ 下载安装包</strong></a>&nbsp;&nbsp;|&nbsp;&nbsp;
    <br />
    <br />
  </p>
</p>
## 📦️ 安装

访问 [Releases](https://github.com/Kaidesuyo/Hydrogen-Music/releases)
页面下载安装包。

## 👷‍♂️ 打包客户端

由于个人设备限制，只打包了Windows平台的安装包且并未适配macOs与Linux平台。
如有可能，您可以在开发环境中自行适配。

## :computer: 配置开发环境

运行本项目

```shell
# 安装依赖
yarn install

# 运行（网页端）
npm run dev

# 运行（electron）
npm start
```

## 📜 开源许可

本项目仅供个人学习研究使用，禁止用于商业及非法用途。

基于 [MIT license](https://opensource.org/licenses/MIT) 许可进行开源。

## 灵感来源

API 源代码来自:
[Binaryify/NeteaseCloudMusicApi](https://github.com/Binaryify/NeteaseCloudMusicApi)
[SocialSisterYi/bilibili-API-collect](https://github.com/SocialSisterYi/bilibili-API-collect)

- [qier222/YesPlayMusic](https://github.com/qier222/YesPlayMusic)
- [Apple Music](https://music.apple.com)
- [网易云音乐](https://music.163.com)

## 🖼️ 截图

![home][home-screenshot]
![playlist][playlist-screenshot]
![lyric][lyric-screenshot]
![music_video][music_video-screenshot]

<!-- MARKDOWN LINKS & IMAGES -->
<!-- https://www.markdownguide.org/basic-syntax/#reference-style-links -->

[home-screenshot]: img/home.png
[playlist-screenshot]: img/playlist.png
[lyric-screenshot]: img/lyric.png
[music_video-screenshot]: img/music_video.png
